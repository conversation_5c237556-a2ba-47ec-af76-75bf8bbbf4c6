'use client';

import { cn } from "@/lib/utils";

export function Loader({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      className={cn("animate-spin h-8 w-8 text-primary", className)}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M12 3a9 9 0 1 0 9 9"
      >
        <animate
          attributeName="d"
          values="M12 3a9 9 0 1 0 9 9;M12 3a9 9 0 0 1 9 9"
          dur="1s"
          repeatCount="indefinite"
        />
      </path>
    </svg>
  );
}