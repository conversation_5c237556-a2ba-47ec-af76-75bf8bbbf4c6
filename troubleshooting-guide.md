# Troubleshooting Guide: Invalid Supabase URL

This guide will help you resolve the `TypeError: Invalid URL` error by correctly configuring your Supabase environment variables.

## The Problem

The error occurs because the application is using placeholder values for the Supabase URL and anonymous key. To fix this, you need to replace these placeholders with your actual Supabase project credentials.

## Step-by-Step Solution

### 1. Locate Your Supabase Credentials

- Go to your Supabase project dashboard.
- In the left sidebar, navigate to **Settings** > **API**.
- You will find your **Project URL** and **Project API keys**.

### 2. Update Your `.env.local` File

- Open the `.env.local` file in your project.
- Copy the **Project URL** and the `public` (anon) key into the corresponding fields:

```
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
```

### 3. Restart Your Development Server

- Stop your development server (usually with `Ctrl+C`).
- Restart it to ensure the new environment variables are loaded.

After following these steps, the application should connect to Supabase without any issues.